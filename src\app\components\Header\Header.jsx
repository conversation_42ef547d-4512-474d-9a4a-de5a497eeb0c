'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import styles from './Header.module.css'

const Header = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        {/* Left navigation */}
        <div className={styles.leftNav}>
          <div className={styles.searchContainer}>
            <svg className={styles.searchIcon} width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <input 
              type="text"
              className={styles.searchInput}
              placeholder="SEARCH"
              aria-label="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onClick={handleSearch}
            />
          </div>
          <nav className={styles.categoryNav}>
            <Link href="/girl" className={styles.navLink}>GIRL</Link>
            <Link href="/boy" className={styles.navLink}>BOY</Link>
            <Link href="/baby" className={styles.navLink}>BABY</Link>
            <Link href="/sell" className={styles.navLink}>SELL</Link>
          </nav>
        </div>

        {/* Logo */}
        <div className={styles.logo}>
          <Link href="/">
            <Image 
              src="/images/header-logo.png" 
              alt="Logo" 
              width={112} 
              height={40} 
            />
          </Link>
        </div>

        {/* Right navigation */}
        <div className={styles.rightNav}>
          <Link href="/favourites" className={styles.navLink}>
            <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 17L8.55 15.7C4.4 12 2 9.9 2 7.2C2 5 3.8 3.2 6 3.2C7.2 3.2 8.4 3.7 9.2 4.5L10 5.3L10.8 4.5C11.6 3.7 12.8 3.2 14 3.2C16.2 3.2 18 5 18 7.2C18 9.9 15.6 12 11.45 15.7L10 17Z" stroke="currentColor" strokeWidth="2"/>
            </svg>
            <span>FAVOURITES</span>
          </Link>
          <Link href="/account" className={styles.navLink}>
            <svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 9C10.2091 9 12 7.20914 12 5C12 2.79086 10.2091 1 8 1C5.79086 1 4 2.79086 4 5C4 7.20914 5.79086 9 8 9Z" stroke="currentColor" strokeWidth="2"/>
              <path d="M15 17C15 13.134 11.866 10 8 10C4.13401 10 1 13.134 1 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
            <span>MY ACCOUNT</span>
          </Link>
          <Link href="/bag" className={styles.navLink}>
            <svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 1L1 4V16C1 16.5304 1.21071 17.0391 1.58579 17.4142C1.96086 17.7893 2.46957 18 3 18H15C15.5304 18 16.0391 17.7893 16.4142 17.4142C16.7893 17.0391 17 16.5304 17 16V4L15 1H3Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M1 4H17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M13 7C13 8.06087 12.5786 9.07828 11.8284 9.82843C11.0783 10.5786 10.0609 11 9 11C7.93913 11 6.92172 10.5786 6.17157 9.82843C5.42143 9.07828 5 8.06087 5 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span>MY BAG</span>
          </Link>
        </div>
      </div>
    </header>
  )
}

export default Header
